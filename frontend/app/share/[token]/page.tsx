"use client"

import { useParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { AlertCircle, Shield, Star } from 'lucide-react'

import { SharedFormRenderer } from '@/components/core/form-share/shared-form-renderer'
import { ShareErrorBoundary } from '@/components/core/form-share/share-error-boundary'
import { PublicAuthProvider } from '@/lib/contexts/public-auth-context'
import { cn } from '@/lib/utils'
import { visualRetreat, mobileRetreat } from '@/lib/utils/responsive'

export default function ShareFormPage() {
  const params = useParams()
  const token = params?.token as string

  // Premium invalid token state
  if (!token) {
    return (
      <div className={cn(mobileRetreat.page.container, "bg-gradient-to-br from-gray-50 via-white to-blue-50/30")}>
        <div className={mobileRetreat.error.container}>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className={cn(visualRetreat.card.base, visualRetreat.card.floating, "max-w-md mx-auto p-8 text-center")}
          >
            <div className="w-16 h-16 mx-auto mb-6 bg-red-50 rounded-full flex items-center justify-center">
              <AlertCircle className="w-8 h-8 text-red-500" />
            </div>

            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Invalid Form Link
            </h1>
            <p className="text-gray-600 mb-6">
              This form sharing link appears to be invalid or malformed. Please check the URL and try again.
            </p>

            <div className="space-y-3">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => window.location.reload()}
                className="w-full px-6 py-3 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-colors touch-target"
              >
                Reload Page
              </motion.button>

              <p className="text-sm text-gray-500">
                If this issue persists, please contact the organization that shared this form.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    )
  }

  // Premium form sharing experience
  return (
    <div className={cn(mobileRetreat.page.container, "bg-gradient-to-br from-gray-50 via-white to-blue-50/30")}>
      {/* Premium trust indicators */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="sticky top-0 z-50 bg-white/95 backdrop-blur-xl border-b border-gray-200/50 safe-top"
      >
        <div className="max-w-4xl mx-auto px-4 py-3 md:px-6">
          <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
            <Shield className="w-4 h-4 text-green-600" />
            <span className="font-medium">Secure Form</span>
            <span className="hidden sm:inline">• Your data is encrypted and protected</span>
          </div>
        </div>
      </motion.div>

      {/* Premium form content */}
      <PublicAuthProvider>
        <ShareErrorBoundary>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          >
            <SharedFormRenderer token={token} />
          </motion.div>
        </ShareErrorBoundary>
      </PublicAuthProvider>

      {/* Premium footer */}
      <motion.footer
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        className="mt-16 py-8 border-t border-gray-200/50"
      >
        <div className="max-w-4xl mx-auto px-4 md:px-6 text-center">
          <div className="flex items-center justify-center gap-2 text-sm text-gray-500 mb-2">
            <Star className="w-4 h-4" />
            <span>Powered by TractionX</span>
          </div>
          <p className="text-xs text-gray-400">
            Premium investment intelligence platform
          </p>
        </div>
      </motion.footer>
    </div>
  )
}
