"use client"

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  Star,
  Heart,
  Share,
  Download,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  TrendingUp,
  Users,
  DollarSign,
  Target,
  CheckCircle,
  AlertCircle,
  XCircle,
  Smartphone,
  Tablet,
  Monitor
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { visualRetreat } from '@/lib/utils/responsive'

const testData = {
  stats: [
    { label: "Active Deals", value: "24", change: "+12%", icon: TrendingUp, color: "green" },
    { label: "Portfolio", value: "156", change: "+8%", icon: Users, color: "blue" },
    { label: "AUM", value: "$45M", change: "+15%", icon: DollarSign, color: "purple" },
    { label: "Hit Rate", value: "73%", change: "+5%", icon: Target, color: "orange" }
  ],
  deals: [
    {
      id: 1,
      company: "TechStart Inc",
      stage: "Series A",
      amount: "$2.5M",
      status: "Active",
      score: 92,
      tags: ["AI", "SaaS", "B2B"]
    },
    {
      id: 2,
      company: "GreenTech Solutions",
      stage: "Seed",
      amount: "$500K",
      status: "Review",
      score: 87,
      tags: ["CleanTech", "Hardware"]
    },
    {
      id: 3,
      company: "FinanceFlow",
      stage: "Series B",
      amount: "$10M",
      status: "Closed",
      score: 95,
      tags: ["FinTech", "Mobile"]
    }
  ]
}

export default function VisualRetreatTest() {
  const [activeTab, setActiveTab] = useState("cards")
  const [showModal, setShowModal] = useState(false)
  const [selectedDevice, setSelectedDevice] = useState("mobile")

  const devices = [
    { id: "mobile", name: "Mobile", icon: Smartphone, width: "375px" },
    { id: "tablet", name: "Tablet", icon: Tablet, width: "768px" },
    { id: "desktop", name: "Desktop", icon: Monitor, width: "100%" }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="sticky top-0 z-40 bg-white/95 backdrop-blur-sm border-b border-gray-200/50 safe-top">
        <div className="px-4 py-4 md:px-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Visual Retreat Test</h1>
              <p className="text-sm text-gray-600">Testing all mobile-first patterns</p>
            </div>
            
            {/* Device Selector */}
            <div className="flex items-center gap-2">
              {devices.map((device) => (
                <Button
                  key={device.id}
                  variant={selectedDevice === device.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedDevice(device.id)}
                  className="flex items-center gap-2"
                >
                  <device.icon className="h-4 w-4" />
                  <span className="hidden sm:inline">{device.name}</span>
                </Button>
              ))}
            </div>
          </div>
        </div>
      </header>

      {/* Test Container */}
      <div className="p-4 md:p-6">
        <div 
          className="mx-auto bg-white rounded-2xl shadow-lg overflow-hidden"
          style={{ 
            width: devices.find(d => d.id === selectedDevice)?.width,
            maxWidth: "100%"
          }}
        >
          {/* Enhanced Tab Navigation */}
          <div className="p-4">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className={visualRetreat.tabs.container}>
                <TabsTrigger 
                  value="cards" 
                  className={cn(
                    visualRetreat.tabs.item,
                    activeTab === "cards" ? visualRetreat.tabs.active : visualRetreat.tabs.inactive
                  )}
                >
                  Cards
                </TabsTrigger>
                <TabsTrigger 
                  value="lists" 
                  className={cn(
                    visualRetreat.tabs.item,
                    activeTab === "lists" ? visualRetreat.tabs.active : visualRetreat.tabs.inactive
                  )}
                >
                  Lists
                </TabsTrigger>
                <TabsTrigger 
                  value="forms" 
                  className={cn(
                    visualRetreat.tabs.item,
                    activeTab === "forms" ? visualRetreat.tabs.active : visualRetreat.tabs.inactive
                  )}
                >
                  Forms
                </TabsTrigger>
                <TabsTrigger 
                  value="modals" 
                  className={cn(
                    visualRetreat.tabs.item,
                    activeTab === "modals" ? visualRetreat.tabs.active : visualRetreat.tabs.inactive
                  )}
                >
                  Modals
                </TabsTrigger>
              </TabsList>

              <div className="mt-6 space-y-6">
                <TabsContent value="cards" className="space-y-6">
                  {/* Stats Grid */}
                  <div className="grid grid-cols-2 gap-4">
                    {testData.stats.map((stat, index) => (
                      <motion.div
                        key={stat.label}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <Card className={cn(visualRetreat.card.base, visualRetreat.card.interactive)}>
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between mb-2">
                              <stat.icon className="h-5 w-5 text-gray-600" />
                              <Badge variant="outline" className="text-xs text-green-600 bg-green-50 border-green-200">
                                {stat.change}
                              </Badge>
                            </div>
                            <div className="space-y-1">
                              <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                              <div className="text-sm text-gray-600">{stat.label}</div>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>

                  {/* Feature Cards */}
                  <div className="space-y-4">
                    <Card className={cn(visualRetreat.card.base, visualRetreat.card.floating)}>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <CheckCircle className="h-5 w-5 text-green-600" />
                          Glassmorphism Cards
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-gray-600">
                          Enhanced cards with backdrop blur and subtle transparency for a premium feel.
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                <TabsContent value="lists" className="space-y-6">
                  <div className={visualRetreat.list.container}>
                    {testData.deals.map((deal, index) => (
                      <motion.div
                        key={deal.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className={cn(visualRetreat.list.item, visualRetreat.card.interactive)}
                      >
                        <div className={visualRetreat.list.content}>
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="font-semibold text-gray-900">{deal.company}</h3>
                            <Badge 
                              variant="outline" 
                              className={cn(
                                "text-xs",
                                deal.status === "Active" ? "bg-green-50 text-green-700 border-green-200" :
                                deal.status === "Review" ? "bg-yellow-50 text-yellow-700 border-yellow-200" :
                                "bg-gray-50 text-gray-700 border-gray-200"
                              )}
                            >
                              {deal.status}
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between text-sm text-gray-600">
                            <span>{deal.stage} • {deal.amount}</span>
                            <div className="flex items-center gap-1">
                              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                              <span className="font-medium">{deal.score}</span>
                            </div>
                          </div>
                          <div className="flex flex-wrap gap-1 mt-2">
                            {deal.tags.map((tag) => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div className={visualRetreat.list.action}>
                          <Button size="icon" variant="ghost" className="touch-target">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="forms" className="space-y-6">
                  <Card className={visualRetreat.card.base}>
                    <CardHeader>
                      <CardTitle>Enhanced Form</CardTitle>
                    </CardHeader>
                    <CardContent className={visualRetreat.form.container}>
                      <div className={visualRetreat.form.field}>
                        <label className="text-sm font-medium text-gray-700">Company Name</label>
                        <Input 
                          placeholder="Enter company name"
                          className={visualRetreat.form.input}
                        />
                      </div>
                      <div className={visualRetreat.form.field}>
                        <label className="text-sm font-medium text-gray-700">Stage</label>
                        <Input 
                          placeholder="e.g., Series A"
                          className={visualRetreat.form.input}
                        />
                      </div>
                      <Button className={visualRetreat.form.button}>
                        Save Changes
                      </Button>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="modals" className="space-y-6">
                  <div className="text-center space-y-4">
                    <p className="text-gray-600">Test the enhanced modal experience</p>
                    <Dialog open={showModal} onOpenChange={setShowModal}>
                      <DialogTrigger asChild>
                        <Button className={visualRetreat.form.button}>
                          Open Modal
                        </Button>
                      </DialogTrigger>
                      <DialogContent className={visualRetreat.modal.content}>
                        <DialogHeader className={visualRetreat.modal.header}>
                          <DialogTitle>Enhanced Modal</DialogTitle>
                        </DialogHeader>
                        <div className={visualRetreat.modal.body}>
                          <p className="text-gray-600">
                            This modal uses the visual retreat pattern with bottom sheet on mobile 
                            and centered modal on desktop.
                          </p>
                        </div>
                        <div className={visualRetreat.modal.footer}>
                          <Button variant="outline" className={visualRetreat.form.button} onClick={() => setShowModal(false)}>
                            Cancel
                          </Button>
                          <Button className={visualRetreat.form.button}>
                            Confirm
                          </Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  )
}
