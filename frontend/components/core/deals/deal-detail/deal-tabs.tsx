"use client"

import { motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { visualRetreat } from "@/lib/utils/responsive"
import { DealDetailData } from "@/lib/types/deal-detail"
import { TimelineTab } from "./timeline-tab"
import { ScoreTab } from "./score-tab"
import { FoundersTab } from "./founders-tab"
import { SignalsTab } from "./signals-tab"
import { AnalystResearchTab } from "./analyst-research-tab"
import { DocumentsTab } from "./documents-tab"
import { BenchmarksTab } from "./benchmarks-tab"
import {
  Clock,
  Target,
  Users,
  Radio,
  Search,
  FileText,
  BarChart3,
  Star
} from "lucide-react"

interface DealTabsProps {
  deal: DealDetailData
  activeTab: string
  onTabChange: (tab: string) => void
}

const tabs = [
  {
    id: 'score',
    label: 'Score',
    icon: Target,
    component: ScoreTab
  },
  {
    id: 'founders',
    label: 'Founders',
    icon: Users,
    component: FoundersTab
  },
  {
    id: 'documents',
    label: 'Documents',
    icon: FileText,
    component: DocumentsTab
  },
  {
    id: 'research',
    label: 'Analyst Research',
    icon: Search,
    component: AnalystResearchTab
  },
  {
    id: 'benchmarks',
    label: 'Benchmarks',
    icon: BarChart3,
    component: BenchmarksTab,
    disabled: true
  },
  {
    id: 'timeline',
    label: 'Timeline',
    icon: Clock,
    component: TimelineTab
  }
]

export function DealTabs({ deal, activeTab, onTabChange }: DealTabsProps) {
  const getTabCount = (tabId: string) => {
    switch (tabId) {
      case 'timeline':
        return deal.timeline?.length || 0
      case 'founders':
        return deal.founders?.length || 0
      case 'research':
        // Count available research components
        const research = deal.analyst_research
        if (!research) return 0
        let count = 0
        if (research.competitors?.competitors?.length) count++
        if (research.market?.market_trends?.length) count++
        if (research.news?.news_signals?.length) count++
        if (research.summary?.executive_summary) count++
        return count
      case 'documents':
        return deal.documents?.length || 0
      default:
        return null
    }
  }

  return (
    <div className="w-full">
      <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
        {/* Premium Tab Navigation with Mobile-First Design */}
        <div className="w-full border-b border-gray-200/50 bg-white/95 backdrop-blur-xl sticky top-[88px] z-30">
          <div className="overflow-x-auto scrollbar-hide">
            <TabsList className={cn(
              visualRetreat.tabs.container,
              "w-full min-w-max grid grid-cols-6 h-auto p-2 bg-transparent border-0 rounded-none gap-2"
            )}>
              {tabs.map((tab, index) => {
                const Icon = tab.icon
                const count = getTabCount(tab.id)

                return (
                  <motion.div
                    key={tab.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <TabsTrigger
                      value={tab.id}
                      disabled={tab.disabled}
                      className={cn(
                        "flex flex-col items-center justify-center gap-2 py-4 px-6 min-w-[120px]",
                        "rounded-xl border border-transparent transition-all duration-200",
                        "data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:border-gray-200/50",
                        "data-[state=active]:text-gray-900 text-gray-600",
                        "hover:bg-gray-50/80 hover:text-gray-900",
                        "disabled:opacity-50 disabled:cursor-not-allowed",
                        "touch-target"
                      )}
                    >
                      <div className="flex items-center gap-2">
                        <Icon className="h-5 w-5" />
                        {count !== null && count > 0 && (
                          <Badge
                            className={cn(
                              "h-5 min-w-[20px] text-xs px-1.5 rounded-full",
                              "bg-gray-100/80 text-gray-700 border border-gray-200/50",
                              "data-[state=active]:bg-blue-50/80 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200/50"
                            )}
                          >
                            {count}
                          </Badge>
                        )}
                      </div>

                      <span className="text-sm font-medium">{tab.label}</span>

                      {tab.disabled && (
                        <Badge
                          className="h-4 text-xs px-2 bg-amber-50/80 text-amber-700 border border-amber-200/50 rounded-full"
                        >
                          Soon
                        </Badge>
                      )}
                    </TabsTrigger>
                  </motion.div>
                )
              })}
            </TabsList>
          </div>
        </div>

        {/* Premium Tab Content with Enhanced Spacing */}
        <div className="w-full">
          {tabs.map((tab) => {
            const Component = tab.component

            return (
              <TabsContent
                key={tab.id}
                value={tab.id}
                className="mt-0 focus-visible:outline-none w-full"
              >
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.4, ease: "easeOut" }}
                  className="w-full"
                >
                  <div className="max-w-7xl mx-auto px-4 py-8 md:px-6 lg:px-8">
                    <Component deal={deal} />
                  </div>
                </motion.div>
              </TabsContent>
            )
          })}
        </div>
      </Tabs>
    </div>
  )
}
