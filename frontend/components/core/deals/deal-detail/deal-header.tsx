"use client"

import { useState } from "react"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  ChevronLeft,
  Share2,
  MessageSquare,
  MoreHorizontal,
  Flag,
  StickyNote,
  Star,
  ExternalLink,
  Copy,
  Download
} from "lucide-react"
import { cn } from "@/lib/utils"
import { visualRetreat } from "@/lib/utils/responsive"
import { DealDetailData } from "@/lib/types/deal-detail"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { toast } from "@/components/ui/use-toast"

interface DealHeaderProps {
  deal: DealDetailData
}

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'new':
      return 'bg-blue-50/80 text-blue-700 border-blue-200/60 before:bg-blue-500'
    case 'active':
      return 'bg-emerald-50/80 text-emerald-700 border-emerald-200/60 before:bg-emerald-500'
    case 'triage':
      return 'bg-amber-50/80 text-amber-700 border-amber-200/60 before:bg-amber-500'
    case 'completed':
      return 'bg-gray-50/80 text-gray-700 border-gray-200/60 before:bg-gray-500'
    case 'flagged':
      return 'bg-red-50/80 text-red-700 border-red-200/60 before:bg-red-500'
    case 'hard_pass':
      return 'bg-red-50/80 text-red-700 border-red-200/60 before:bg-red-500'
    default:
      return 'bg-gray-50/80 text-gray-700 border-gray-200/60 before:bg-gray-500'
  }
}

const getStageColor = (stage: string) => {
  switch (stage?.toLowerCase()) {
    case 'pre-seed':
      return 'bg-purple-50/80 text-purple-700 border-purple-200/60'
    case 'seed':
      return 'bg-blue-50/80 text-blue-700 border-blue-200/60'
    case 'series a':
      return 'bg-emerald-50/80 text-emerald-700 border-emerald-200/60'
    case 'series b':
      return 'bg-orange-50/80 text-orange-700 border-orange-200/60'
    case 'series c':
      return 'bg-red-50/80 text-red-700 border-red-200/60'
    default:
      return 'bg-gray-50/80 text-gray-700 border-gray-200/60'
  }
}

const getScoreColor = (score: number) => {
  if (score >= 80) return 'from-green-500 to-emerald-600'
  if (score >= 60) return 'from-yellow-500 to-orange-500'
  return 'from-red-500 to-red-600'
}

const formatTimeAgo = (timestamp: string | number) => {
  const date = new Date(typeof timestamp === 'string' ? timestamp : timestamp * 1000)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return 'just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  return `${Math.floor(diffInSeconds / 86400)}d ago`
}

export function DealHeader({ deal }: DealHeaderProps) {
  const [isStarred, setIsStarred] = useState(false)

  const handleStarToggle = () => {
    setIsStarred(!isStarred)
    toast({
      title: isStarred ? "Removed from favorites" : "Added to favorites",
      description: `${deal.company_name} has been ${isStarred ? 'removed from' : 'added to'} your favorites.`,
    })
  }

  const handleShare = () => {
    navigator.clipboard.writeText(window.location.href)
    toast({
      title: "Link copied",
      description: "Deal link has been copied to your clipboard.",
    })
  }

  const handleCreateMemo = () => {
    toast({
      title: "AI Memo Generation",
      description: "Starting AI-powered memo generation...",
    })
    // TODO: Implement deal memo creation
  }

  const humanize = (key: string) => {
    return key
      .split('_')
      .map(part =>
        part.length <= 2
          ? part.toUpperCase()                                   // e.g. 'ai' → 'AI'
          : part[0].toUpperCase() + part.slice(1).toLowerCase()  // e.g. 'pre' → 'Pre'
      )
      .join('-');                                              // e.g. ['Pre','Seed'] → 'Pre-Seed'
  }

  const overallScore = deal.score_breakdown?.overall_score || 0
  const foundersCount = deal.founders?.length || 0
  const documentsCount = deal.documents?.length || 0
  const signalsCount = deal.external_signals?.length || 0
  const lastUpdated = formatTimeAgo(deal.updated_at)

  return (
    <div className="space-y-8">
      {/* Premium Breadcrumb */}
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        className="flex items-center"
      >
        <Link
          href="/deals"
          className="group flex items-center text-sm text-gray-600 hover:text-gray-900 transition-all duration-200"
        >
          <ChevronLeft className="h-4 w-4 mr-1 group-hover:-translate-x-0.5 transition-transform" />
          <span className="font-medium">Back to Deals</span>
        </Link>
      </motion.div>

      {/* Premium Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-8"
      >
        {/* Left Side - Company Information */}
        <div className="space-y-6 flex-1">
          {/* Company Name with premium typography */}
          <div className="space-y-2">
            <motion.h1
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-4xl md:text-5xl font-bold tracking-tight text-gray-900 leading-tight"
            >
              {deal.company_name || 'Unnamed Company'}
            </motion.h1>

            {/* Website link if available */}
            {deal.company_website && (
              <motion.a
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
                href={deal.company_website}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-700 transition-colors group"
              >
                <span className="text-sm font-medium">{deal.company_website.replace(/^https?:\/\//, '')}</span>
                <ExternalLink className="h-3 w-3 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform" />
              </motion.a>
            )}
          </div>

          {/* Premium Status Chips */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="flex flex-wrap items-center gap-3"
          >
            {/* Enhanced Status Badge */}
            <Badge
              className={cn(
                "relative pl-6 font-medium border backdrop-blur-sm",
                getStatusColor(deal.status),
                "before:absolute before:left-2.5 before:top-1/2 before:-translate-y-1/2 before:w-2 before:h-2 before:rounded-full before:shadow-sm"
              )}
            >
              {deal.status.charAt(0).toUpperCase() + deal.status.slice(1).replace('_', ' ')}
            </Badge>

            {/* Enhanced Stage Badge */}
            {deal.stage && (
              <Badge className={cn("font-medium border backdrop-blur-sm", getStageColor(deal.stage))}>
                {humanize(deal.stage)}
              </Badge>
            )}

            {/* Premium Sector Badges */}
            {deal.sector && (
              <>
                {Array.isArray(deal.sector) ? (
                  <>
                    <Badge className="bg-gray-100/80 text-gray-700 border border-gray-200/60 font-medium backdrop-blur-sm">
                      {humanize(deal.sector[0])}
                    </Badge>
                    {deal.sector.length > 1 && (
                      <Badge
                        className="bg-gray-100/80 text-gray-700 border border-gray-200/60 font-medium backdrop-blur-sm cursor-pointer hover:bg-gray-200/80 transition-colors"
                        title={deal.sector.slice(1).map(s => humanize(s)).join(', ')}
                      >
                        {deal.sector.length === 2 ? humanize(deal.sector[1]) : `+${deal.sector.length - 1} more`}
                      </Badge>
                    )}
                  </>
                ) : (
                  <Badge className="bg-gray-100/80 text-gray-700 border border-gray-200/60 font-medium backdrop-blur-sm">
                    {deal.sector}
                  </Badge>
                )}
              </>
            )}

            {/* Premium Score Badge */}
            {overallScore > 0 && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.5, type: "spring" }}
              >
                <Badge
                  className={cn(
                    "px-4 py-2 text-lg font-bold border-2 bg-white/95 backdrop-blur-sm shadow-sm",
                    overallScore >= 80 ? "border-emerald-500 text-emerald-700" :
                    overallScore >= 60 ? "border-amber-500 text-amber-700" :
                    "border-red-500 text-red-700"
                  )}
                >
                  <Star className="h-4 w-4 mr-1" />
                  {overallScore}
                </Badge>
              </motion.div>
            )}
          </motion.div>

          {/* Premium Meta Information */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="flex flex-wrap items-center gap-4 text-sm text-gray-600"
          >
            <div className="flex items-center gap-1">
              <div className="w-1.5 h-1.5 bg-gray-400 rounded-full" />
              <span className="font-medium">{foundersCount}</span>
              <span>founder{foundersCount !== 1 ? 's' : ''}</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-1.5 h-1.5 bg-gray-400 rounded-full" />
              <span className="font-medium">{documentsCount}</span>
              <span>document{documentsCount !== 1 ? 's' : ''}</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-1.5 h-1.5 bg-gray-400 rounded-full" />
              <span className="font-medium">{signalsCount}</span>
              <span>signal{signalsCount !== 1 ? 's' : ''}</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-1.5 h-1.5 bg-gray-400 rounded-full" />
              <span>Updated {lastUpdated}</span>
            </div>
          </motion.div>
        </div>

        {/* Premium Action Buttons */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="flex items-center gap-3 lg:flex-shrink-0"
        >
          {/* Premium Star Button */}
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button
              variant="outline"
              size="sm"
              onClick={handleStarToggle}
              className={cn(
                "transition-all duration-200 border backdrop-blur-sm touch-target",
                isStarred
                  ? "bg-amber-50/80 border-amber-200/60 text-amber-700 hover:bg-amber-100/80"
                  : "bg-white/80 border-gray-200/60 hover:bg-gray-50/80"
              )}
            >
              <Star className={cn("h-4 w-4", isStarred && "fill-current")} />
            </Button>
          </motion.div>

          {/* Premium AI Memo Button */}
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <div className="flex flex-col items-center">
              <Button
                onClick={handleCreateMemo}
                className="gap-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg shadow-blue-500/25 transition-all duration-200 touch-target"
              >
                <Star className="h-4 w-4" />
                <span className="hidden sm:inline">AI Memo</span>
                <span className="sm:hidden">Memo</span>
              </Button>
              <span className="text-xs text-gray-500 mt-1 hidden lg:block">Powered by AI</span>
            </div>
          </motion.div>

          {/* Premium Share Button */}
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button
              variant="ghost"
              onClick={handleShare}
              className="gap-2 hover:bg-gray-50/80 transition-all duration-200 touch-target"
            >
              <Share2 className="h-4 w-4" />
              <span className="hidden sm:inline">Share</span>
            </Button>
          </motion.div>

          {/* Premium Inbox Button */}
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button
              variant="ghost"
              className="gap-2 hover:bg-gray-50/80 transition-all duration-200 touch-target"
            >
              <MessageSquare className="h-4 w-4" />
              <span className="hidden sm:inline">Inbox</span>
            </Button>
          </motion.div>

          {/* Premium More Actions */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant="ghost"
                  size="sm"
                  className="hover:bg-gray-50/80 transition-all duration-200 touch-target"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </motion.div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className={cn(visualRetreat.card.base, "w-48 border-gray-200/60")}>
              <DropdownMenuItem className="hover:bg-gray-50/80 transition-colors">
                <Copy className="h-4 w-4 mr-2" />
                Copy Link
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:bg-gray-50/80 transition-colors">
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="hover:bg-gray-50/80 transition-colors">
                <Flag className="h-4 w-4 mr-2" />
                Move to Folder
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600 hover:bg-red-50/80 transition-colors">
                <Flag className="h-4 w-4 mr-2" />
                Flag Deal
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600 hover:bg-red-50/80 transition-colors">
                Archive Deal
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </motion.div>
      </motion.div>
    </div>
  )
}
