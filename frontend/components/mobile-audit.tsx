"use client"

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Smartphone, 
  Tablet, 
  Monitor, 
  CheckCircle, 
  AlertCircle, 
  XCircle,
  Eye,
  Touch,
  Zap,
  Layers
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { visualRetreat } from '@/lib/utils/responsive'

interface AuditResult {
  page: string
  route: string
  status: 'pass' | 'warning' | 'fail'
  issues: string[]
  score: number
}

interface DeviceTest {
  name: string
  width: number
  height: number
  icon: React.ComponentType<{ className?: string }>
}

const devices: DeviceTest[] = [
  { name: 'iPhone SE', width: 375, height: 667, icon: Smartphone },
  { name: 'iPhone 15 Pro', width: 393, height: 852, icon: Smartphone },
  { name: 'Pixel 7', width: 412, height: 915, icon: Smartphone },
  { name: 'iPad Mini', width: 768, height: 1024, icon: Tablet },
  { name: 'Desktop', width: 1440, height: 900, icon: Monitor },
]

const testPages = [
  { name: 'Dashboard', route: '/dashboard' },
  { name: 'Deals', route: '/deals' },
  { name: 'Forms', route: '/forms' },
  { name: 'Theses', route: '/theses' },
  { name: 'Settings', route: '/settings' },
  { name: 'Deal Detail', route: '/deals/[id]' },
  { name: 'Form Builder', route: '/forms/[id]/edit' },
  { name: 'Thesis Builder', route: '/theses/[id]/edit' },
]

export function MobileAudit() {
  const [selectedDevice, setSelectedDevice] = useState(devices[0])
  const [auditResults, setAuditResults] = useState<AuditResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [currentTest, setCurrentTest] = useState<string | null>(null)

  // Simulate audit process
  const runAudit = async () => {
    setIsRunning(true)
    setAuditResults([])
    
    for (const page of testPages) {
      setCurrentTest(page.name)
      
      // Simulate testing delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock audit results - in real implementation, this would test actual pages
      const mockResult: AuditResult = {
        page: page.name,
        route: page.route,
        status: Math.random() > 0.7 ? 'pass' : Math.random() > 0.3 ? 'warning' : 'fail',
        issues: generateMockIssues(),
        score: Math.floor(Math.random() * 40) + 60
      }
      
      setAuditResults(prev => [...prev, mockResult])
    }
    
    setCurrentTest(null)
    setIsRunning(false)
  }

  const generateMockIssues = (): string[] => {
    const possibleIssues = [
      'Touch targets smaller than 44px',
      'Text below 16px on mobile',
      'Horizontal scroll detected',
      'Modal not using bottom sheet pattern',
      'Missing safe area padding',
      'Animation performance issues',
      'Inconsistent spacing',
      'Poor contrast ratios'
    ]
    
    const numIssues = Math.floor(Math.random() * 3)
    return possibleIssues.slice(0, numIssues)
  }

  const getStatusIcon = (status: AuditResult['status']) => {
    switch (status) {
      case 'pass': return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'warning': return <AlertCircle className="h-4 w-4 text-yellow-600" />
      case 'fail': return <XCircle className="h-4 w-4 text-red-600" />
    }
  }

  const getStatusColor = (status: AuditResult['status']) => {
    switch (status) {
      case 'pass': return 'bg-green-50 text-green-700 border-green-200'
      case 'warning': return 'bg-yellow-50 text-yellow-700 border-yellow-200'
      case 'fail': return 'bg-red-50 text-red-700 border-red-200'
    }
  }

  const overallScore = auditResults.length > 0 
    ? Math.round(auditResults.reduce((sum, result) => sum + result.score, 0) / auditResults.length)
    : 0

  return (
    <div className="min-h-screen bg-gray-50 p-4 md:p-6 lg:p-8">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900">
            Mobile-First Visual Retreat Audit
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Comprehensive testing across all devices to ensure every page is a visual retreat
          </p>
        </div>

        {/* Device Selector */}
        <Card className={visualRetreat.card.base}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Device Testing
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
              {devices.map((device) => (
                <Button
                  key={device.name}
                  variant={selectedDevice.name === device.name ? "default" : "outline"}
                  className="flex flex-col items-center gap-2 h-auto py-4"
                  onClick={() => setSelectedDevice(device)}
                >
                  <device.icon className="h-5 w-5" />
                  <div className="text-center">
                    <div className="font-medium text-sm">{device.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {device.width}×{device.height}
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Audit Controls */}
        <Card className={visualRetreat.card.base}>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row items-center justify-between gap-4">
              <div className="flex items-center gap-4">
                <Button 
                  onClick={runAudit} 
                  disabled={isRunning}
                  size="lg"
                  className="min-w-[140px]"
                >
                  {isRunning ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                      Running...
                    </>
                  ) : (
                    <>
                      <Zap className="h-4 w-4 mr-2" />
                      Run Full Audit
                    </>
                  )}
                </Button>
                
                {currentTest && (
                  <div className="text-sm text-gray-600">
                    Testing: <span className="font-medium">{currentTest}</span>
                  </div>
                )}
              </div>
              
              {auditResults.length > 0 && (
                <div className="flex items-center gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">{overallScore}%</div>
                    <div className="text-sm text-gray-600">Overall Score</div>
                  </div>
                  <Badge 
                    variant="outline" 
                    className={cn(
                      "text-sm",
                      overallScore >= 90 ? "border-green-200 text-green-700 bg-green-50" :
                      overallScore >= 70 ? "border-yellow-200 text-yellow-700 bg-yellow-50" :
                      "border-red-200 text-red-700 bg-red-50"
                    )}
                  >
                    {overallScore >= 90 ? 'Excellent' : overallScore >= 70 ? 'Good' : 'Needs Work'}
                  </Badge>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Results */}
        <AnimatePresence>
          {auditResults.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-4"
            >
              <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                <Layers className="h-6 w-6" />
                Audit Results
              </h2>
              
              <div className="grid gap-4">
                {auditResults.map((result, index) => (
                  <motion.div
                    key={result.page}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card className={cn(visualRetreat.card.base, "border-l-4", {
                      "border-l-green-500": result.status === 'pass',
                      "border-l-yellow-500": result.status === 'warning',
                      "border-l-red-500": result.status === 'fail',
                    })}>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center gap-3">
                            {getStatusIcon(result.status)}
                            <h3 className="font-semibold text-lg">{result.page}</h3>
                            <Badge variant="outline" className="text-xs">
                              {result.route}
                            </Badge>
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold">{result.score}%</div>
                            <Badge className={cn("text-xs", getStatusColor(result.status))}>
                              {result.status.toUpperCase()}
                            </Badge>
                          </div>
                        </div>
                        
                        {result.issues.length > 0 && (
                          <div className="space-y-2">
                            <h4 className="font-medium text-sm text-gray-700">Issues Found:</h4>
                            <ul className="space-y-1">
                              {result.issues.map((issue, i) => (
                                <li key={i} className="text-sm text-gray-600 flex items-center gap-2">
                                  <div className="w-1 h-1 bg-gray-400 rounded-full" />
                                  {issue}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}
