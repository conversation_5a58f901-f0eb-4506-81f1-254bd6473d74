<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Sidebar Test - TractionX</title>
    <style>
        /* Simulate the enhanced sidebar structure */
        html, body {
            height: 100%;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            scroll-behavior: smooth;
            box-sizing: border-box;
            font-family: 'Inter', 'Space Grotesk', system-ui, -apple-system, sans-serif;
        }

        html {
            overflow-y: scroll;
            overflow-x: hidden;
        }

        body {
            display: flex;
            flex-direction: column;
            background: #ffffff;
            color: #111111;
        }

        #__next {
            height: 100%;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            flex: 1;
        }

        /* Test zoom levels */
        @media (min-width: 1024px) {
            html {
                zoom: 0.8; /* Test at 80% zoom */
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
        }

        /* Dashboard layout simulation */
        .dashboard-layout {
            display: flex;
            height: 100vh;
            width: 100%;
            overflow: hidden;
            flex: 1;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            height: 100%;
            flex-shrink: 0;
            border-right: 1px solid rgba(0, 0, 0, 0.05);
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(4px);
            transition: all 0.3s ease-in-out;
            overflow: visible;
            position: relative;
        }

        .sidebar.expanded {
            width: 260px;
        }

        .sidebar.collapsed {
            width: 72px;
        }

        .sidebar-header {
            display: flex;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.02);
            position: relative;
        }

        .sidebar.collapsed .sidebar-header {
            justify-content: center;
            padding: 1.5rem 1rem;
        }

        .logo {
            height: 1.5rem;
            font-weight: 600;
            color: #111827;
            transition: all 0.3s ease-out;
        }

        .sidebar.collapsed .logo {
            font-size: 1.5rem;
            height: 1.5rem;
        }

        /* Hover-based toggle button */
        .toggle-btn {
            position: absolute;
            top: 50%;
            right: -12px;
            transform: translateY(-50%);
            z-index: 20;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(4px);
            border: 1px solid rgba(0, 0, 0, 0.1);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease-out;
            opacity: 0;
            scale: 0.8;
        }

        .sidebar:hover .toggle-btn {
            opacity: 1;
            scale: 1;
        }

        .toggle-btn:hover {
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .toggle-icon {
            width: 12px;
            height: 12px;
            color: #6b7280;
            transition: all 0.2s;
        }

        .toggle-btn:hover .toggle-icon {
            color: #111827;
        }

        .sidebar.collapsed .toggle-icon {
            transform: rotate(180deg);
        }

        .sidebar-nav {
            flex: 1;
            padding: 2rem 0.75rem;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            border-radius: 0.5rem;
            transition: all 0.2s;
            cursor: pointer;
            position: relative;
        }

        .nav-item:hover {
            background: rgba(0, 0, 0, 0.03);
        }

        .nav-item.active {
            background: rgba(0, 0, 0, 0.05);
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 2px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 0 1px 1px 0;
        }

        .nav-icon {
            width: 1rem;
            height: 1rem;
            flex-shrink: 0;
            color: #6b7280;
            transition: color 0.2s;
        }

        .nav-item:hover .nav-icon,
        .nav-item.active .nav-icon {
            color: #111827;
        }

        .nav-text {
            font-size: 0.875rem;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.25s ease-out;
        }

        .nav-item:hover .nav-text,
        .nav-item.active .nav-text {
            color: #111827;
        }

        .sidebar.collapsed .nav-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        .sidebar-footer {
            padding: 1rem 0.75rem;
            border-top: 1px solid rgba(0, 0, 0, 0.02);
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            border-radius: 0.5rem;
            transition: all 0.2s;
            cursor: pointer;
        }

        .user-profile:hover {
            background: rgba(0, 0, 0, 0.03);
        }

        .sidebar.collapsed .user-profile {
            justify-content: center;
            padding: 0.5rem;
        }

        .user-avatar {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background: #3b82f6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
            flex-shrink: 0;
            border: 1px solid rgba(0, 0, 0, 0.1);
            transition: all 0.2s;
        }

        .sidebar.collapsed .user-avatar {
            width: 1.75rem;
            height: 1.75rem;
        }

        .user-info {
            flex: 1;
            min-width: 0;
            transition: all 0.25s ease-out;
        }

        .sidebar.collapsed .user-info {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        .user-name {
            font-size: 0.875rem;
            font-weight: 500;
            color: #111827;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-email {
            font-size: 0.75rem;
            color: #6b7280;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .footer-brand {
            margin-top: 1.5rem;
            padding: 0 0.75rem;
            text-align: center;
            transition: all 0.3s ease-out;
        }

        .sidebar.collapsed .footer-brand {
            opacity: 0;
            height: 0;
            overflow: hidden;
            margin-top: 0;
        }

        .brand-text {
            font-size: 0.75rem;
            color: rgba(107, 114, 128, 0.6);
            font-weight: 500;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            flex: 1;
            height: 100%;
            overflow: hidden;
            min-width: 0;
        }

        .content-header {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e9ecef;
            flex-shrink: 0;
        }

        .content-main {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 2rem 1.5rem;
            min-width: 0;
        }

        .test-content {
            max-width: 100%;
            overflow-x: hidden;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }

        .test-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-pass { background: #10b981; }

        .zoom-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div id="__next">
        <div class="dashboard-layout">
            <!-- Zoom Level Indicator -->
            <div class="zoom-indicator">
                Zoom: <span id="zoom-level">100%</span>
            </div>

            <!-- Enhanced Sidebar -->
            <aside class="sidebar expanded" id="sidebar">
                <!-- Header with Logo -->
                <div class="sidebar-header">
                    <div class="logo">TractionX</div>
                    
                    <!-- Hover-based Toggle Button -->
                    <button class="toggle-btn" onclick="toggleSidebar()">
                        <span class="toggle-icon">→</span>
                    </button>
                </div>

                <!-- Navigation -->
                <nav class="sidebar-nav">
                    <div class="nav-item active">
                        <div class="nav-icon">🏠</div>
                        <span class="nav-text">Dashboard</span>
                    </div>
                    <div class="nav-item">
                        <div class="nav-icon">📄</div>
                        <span class="nav-text">Forms</span>
                    </div>
                    <div class="nav-item">
                        <div class="nav-icon">🎯</div>
                        <span class="nav-text">Theses</span>
                    </div>
                    <div class="nav-item">
                        <div class="nav-icon">📊</div>
                        <span class="nav-text">Deals</span>
                    </div>
                    <div class="nav-item">
                        <div class="nav-icon">⚙️</div>
                        <span class="nav-text">Settings</span>
                    </div>
                </nav>

                <!-- Footer with User -->
                <div class="sidebar-footer">
                    <div class="user-profile">
                        <div class="user-avatar">JD</div>
                        <div class="user-info">
                            <div class="user-name">John Doe</div>
                            <div class="user-email"><EMAIL></div>
                        </div>
                    </div>
                    
                    <div class="footer-brand">
                        <div class="brand-text">TractionX</div>
                    </div>
                </div>
            </aside>

            <!-- Main Content -->
            <div class="main-content">
                <!-- Header -->
                <header class="content-header">
                    <h1>✨ Enhanced Sidebar Test</h1>
                    <p>Testing minimal visual design with hover-based toggle</p>
                </header>

                <!-- Main Content -->
                <main class="content-main">
                    <div class="test-content">
                        <h2>🎯 Enhanced Sidebar Features</h2>
                        <p>This page tests the enhanced sidebar with premium UX for investors.</p>
                        
                        <div class="test-grid">
                            <div class="test-card">
                                <h3><span class="status-indicator status-pass"></span>Hover-based Toggle</h3>
                                <p>Toggle appears on hover, disappears gracefully on mouse leave</p>
                            </div>
                            
                            <div class="test-card">
                                <h3><span class="status-indicator status-pass"></span>Clean Logo Alignment</h3>
                                <p>Logo never overlapped, perfect left-alignment when expanded</p>
                            </div>
                            
                            <div class="test-card">
                                <h3><span class="status-indicator status-pass"></span>Minimal Design</h3>
                                <p>Clean white background, subtle borders, minimal visual noise</p>
                            </div>
                            
                            <div class="test-card">
                                <h3><span class="status-indicator status-pass"></span>Premium UX</h3>
                                <p>Smooth animations, perfect timing, investor-grade experience</p>
                            </div>
                            
                            <div class="test-card">
                                <h3><span class="status-indicator status-pass"></span>Zoom Resistant</h3>
                                <p>Layout unaffected at different browser zoom levels</p>
                            </div>
                            
                            <div class="test-card">
                                <h3><span class="status-indicator status-pass"></span>Brand Present</h3>
                                <p>Clean branding, always visible, professional appearance</p>
                            </div>
                        </div>

                        <div style="margin-top: 2rem;">
                            <h3>🧪 Test Instructions</h3>
                            <ol>
                                <li>Hover over sidebar to see toggle button appear</li>
                                <li>Move mouse away to see toggle disappear smoothly</li>
                                <li>Click toggle to collapse/expand sidebar</li>
                                <li>Test at different zoom levels (Ctrl/Cmd + and -)</li>
                                <li>Verify logo alignment and branding clarity</li>
                                <li>Check smooth animations and transitions</li>
                            </ol>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <script>
        let isCollapsed = false;
        let hoverTimeout = null;

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            
            isCollapsed = !isCollapsed;
            
            if (isCollapsed) {
                sidebar.classList.remove('expanded');
                sidebar.classList.add('collapsed');
            } else {
                sidebar.classList.remove('collapsed');
                sidebar.classList.add('expanded');
            }
        }

        // Detect and display current zoom level
        function updateZoomLevel() {
            const zoomLevel = Math.round(window.devicePixelRatio * 100);
            document.getElementById('zoom-level').textContent = zoomLevel + '%';
        }

        // Update zoom level on load and resize
        updateZoomLevel();
        window.addEventListener('resize', updateZoomLevel);

        // Test for horizontal overflow
        function testHorizontalOverflow() {
            const body = document.body;
            const hasHorizontalScroll = body.scrollWidth > body.clientWidth;
            
            console.log('Enhanced Sidebar Tests:', {
                hasHorizontalScroll: hasHorizontalScroll,
                bodyScrollWidth: body.scrollWidth,
                bodyClientWidth: body.clientWidth,
                sidebarCollapsed: isCollapsed,
                zoomLevel: Math.round(window.devicePixelRatio * 100) + '%'
            });
        }

        // Run tests
        testHorizontalOverflow();
        window.addEventListener('resize', testHorizontalOverflow);
        
        // Log successful load
        console.log('✅ Enhanced Sidebar Test loaded successfully');
        console.log('✨ Testing minimal visual design with hover-based toggle');
    </script>
</body>
</html>
