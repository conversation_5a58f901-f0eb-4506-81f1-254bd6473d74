/**
 * Mobile-First Responsive Utilities for TractionX
 * 
 * This file contains utility functions and constants for implementing
 * mobile-first, Notion-grade responsive design patterns.
 */

import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

// PRD-compliant breakpoints
export const BREAKPOINTS = {
  xs: 480,   // Mobile small
  sm: 640,   // Mobile large  
  md: 1024,  // Tablet
  lg: 1440,  // Desktop
  xl: 1920,  // Large desktop
} as const

// Mobile-first responsive class generators
export const responsive = {
  // Grid utilities
  grid: {
    mobile: "grid grid-cols-1 gap-4",
    tablet: "xs:grid-cols-2 md:gap-6",
    desktop: "md:grid-cols-3 lg:gap-8",
    dashboard: "grid grid-cols-1 xs:grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 lg:gap-8",
  },
  
  // Padding utilities
  padding: {
    mobile: "p-4",
    tablet: "md:p-6", 
    desktop: "lg:p-8",
    section: "px-4 py-6 md:px-6 md:py-8 lg:px-8 lg:py-10",
    card: "p-4 md:p-6 lg:p-8",
  },
  
  // Typography utilities
  text: {
    heading: "text-2xl md:text-3xl lg:text-4xl",
    subheading: "text-xl md:text-2xl lg:text-3xl",
    body: "text-base md:text-lg",
    small: "text-sm md:text-base",
    caption: "text-xs md:text-sm",
  },
  
  // Layout utilities
  layout: {
    container: "w-full max-w-none px-4 md:px-6 lg:px-8",
    sidebar: "w-full max-w-sm md:max-w-md lg:max-w-lg",
    modal: "w-full max-w-md md:max-w-lg lg:max-w-xl",
  },
  
  // Touch targets
  touch: {
    button: "h-12 min-w-[120px] touch-target",
    icon: "h-12 w-12 touch-target",
    input: "h-12 touch-target",
  },
} as const

// Mobile-first component class generators
export function mobileFirst(classes: {
  mobile: string
  tablet?: string
  desktop?: string
  large?: string
}): string {
  return clsx(
    classes.mobile,
    classes.tablet && `md:${classes.tablet}`,
    classes.desktop && `lg:${classes.desktop}`,
    classes.large && `xl:${classes.large}`
  )
}

// Responsive grid generator
export function responsiveGrid(
  cols: { mobile: number; tablet?: number; desktop?: number; large?: number },
  gap: { mobile: number; tablet?: number; desktop?: number } = { mobile: 4 }
): string {
  const gridCols = {
    1: "grid-cols-1",
    2: "grid-cols-2", 
    3: "grid-cols-3",
    4: "grid-cols-4",
    5: "grid-cols-5",
    6: "grid-cols-6",
  } as const
  
  const gridGaps = {
    2: "gap-2",
    3: "gap-3",
    4: "gap-4",
    6: "gap-6",
    8: "gap-8",
  } as const
  
  return clsx(
    "grid",
    gridCols[cols.mobile as keyof typeof gridCols],
    cols.tablet && `xs:${gridCols[cols.tablet as keyof typeof gridCols]}`,
    cols.desktop && `md:${gridCols[cols.desktop as keyof typeof gridCols]}`,
    cols.large && `lg:${gridCols[cols.large as keyof typeof gridCols]}`,
    gridGaps[gap.mobile as keyof typeof gridGaps],
    gap.tablet && `md:${gridGaps[gap.tablet as keyof typeof gridGaps]}`,
    gap.desktop && `lg:${gridGaps[gap.desktop as keyof typeof gridGaps]}`
  )
}

// Mobile-first spacing generator
export function responsiveSpacing(
  type: "p" | "px" | "py" | "pt" | "pb" | "pl" | "pr" | "m" | "mx" | "my" | "mt" | "mb" | "ml" | "mr",
  sizes: { mobile: number; tablet?: number; desktop?: number }
): string {
  const sizeMap = {
    0: "0", 1: "1", 2: "2", 3: "3", 4: "4", 5: "5", 6: "6", 8: "8", 10: "10", 12: "12", 16: "16", 20: "20", 24: "24"
  } as const
  
  return clsx(
    `${type}-${sizeMap[sizes.mobile as keyof typeof sizeMap]}`,
    sizes.tablet && `md:${type}-${sizeMap[sizes.tablet as keyof typeof sizeMap]}`,
    sizes.desktop && `lg:${type}-${sizeMap[sizes.desktop as keyof typeof sizeMap]}`
  )
}

// Device detection utilities (client-side only)
export const device = {
  isMobile: () => typeof window !== 'undefined' && window.innerWidth < BREAKPOINTS.md,
  isTablet: () => typeof window !== 'undefined' && window.innerWidth >= BREAKPOINTS.md && window.innerWidth < BREAKPOINTS.lg,
  isDesktop: () => typeof window !== 'undefined' && window.innerWidth >= BREAKPOINTS.lg,
  
  // Touch device detection
  isTouch: () => typeof window !== 'undefined' && ('ontouchstart' in window || navigator.maxTouchPoints > 0),
  
  // Safe area detection
  hasSafeArea: () => typeof window !== 'undefined' && CSS.supports('padding-top: env(safe-area-inset-top)'),
}

// Mobile-first animation variants for Framer Motion
export const mobileAnimations = {
  // Slide animations optimized for mobile
  slideUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: 0.3, ease: "easeOut" }
  },
  
  slideLeft: {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 },
    transition: { duration: 0.3, ease: "easeOut" }
  },
  
  // Scale animations for touch feedback
  touchScale: {
    whileTap: { scale: 0.98 },
    transition: { duration: 0.1 }
  },
  
  // Stagger animations for lists
  stagger: {
    container: {
      animate: {
        transition: {
          staggerChildren: 0.1
        }
      }
    },
    item: {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 },
      transition: { duration: 0.3 }
    }
  }
}

// Mobile-first form utilities
export const mobileForm = {
  input: "h-12 w-full rounded-xl border border-input bg-transparent px-4 py-3 text-base touch-target",
  button: "h-12 w-full rounded-xl font-bold transition-all duration-200 touch-target active:scale-[0.98]",
  label: "text-sm font-medium mb-2 block",
  error: "text-sm text-destructive mt-1",
  fieldset: "space-y-4 md:space-y-6",
}

// Mobile-first modal/dialog utilities
export const mobileModal = {
  overlay: "fixed inset-0 z-50 bg-background/80 backdrop-blur-sm",
  content: [
    "fixed z-50 grid w-full gap-4 border bg-background shadow-lg",
    // Mobile: bottom sheet
    "bottom-0 rounded-t-2xl p-6 max-h-[90vh] overflow-y-auto safe-bottom",
    // Tablet+: centered modal
    "xs:bottom-auto xs:top-1/2 xs:left-1/2 xs:-translate-x-1/2 xs:-translate-y-1/2",
    "xs:max-w-lg xs:rounded-2xl xs:max-h-[85vh]"
  ].join(" "),
  close: "absolute right-4 top-4 rounded-xl p-2 opacity-70 hover:opacity-100 hover:bg-accent touch-target",
}

// Mobile-first navigation utilities
export const mobileNav = {
  hamburger: "md:hidden rounded-xl p-2 hover:bg-accent active:scale-95 touch-target",
  overlay: "fixed inset-0 z-50 bg-background/80 backdrop-blur-sm md:hidden",
  panel: [
    "fixed inset-y-0 left-0 w-full max-w-sm bg-background border-r shadow-xl",
    "safe-top safe-bottom"
  ].join(" "),
  item: [
    "flex w-full items-center rounded-xl p-4 text-base font-medium",
    "transition-all duration-200 touch-target",
    "hover:bg-accent hover:text-accent-foreground active:scale-[0.98]"
  ].join(" "),
}

// Enhanced mobile-first patterns for visual retreat
export const visualRetreat = {
  // Card patterns with glassmorphism
  card: {
    base: [
      "bg-white/98 backdrop-blur-sm border border-gray-200/50",
      "rounded-2xl shadow-sm hover:shadow-lg",
      "transition-all duration-300 ease-out",
      "touch-manipulation"
    ].join(" "),
    interactive: [
      "cursor-pointer hover:scale-[1.02] active:scale-[0.98]",
      "hover:bg-white hover:shadow-xl hover:border-gray-300/50"
    ].join(" "),
    floating: [
      "shadow-lg hover:shadow-xl",
      "border-gray-100 bg-white/95"
    ].join(" ")
  },

  // Tab patterns with animated indicators
  tabs: {
    container: "flex overflow-x-auto scrollbar-hide gap-2 p-1 bg-gray-50/50 rounded-xl",
    item: [
      "flex-shrink-0 px-4 py-2 rounded-lg text-sm font-medium",
      "transition-all duration-200 touch-target min-w-[80px]",
      "hover:bg-white/80 active:scale-95"
    ].join(" "),
    active: "bg-white shadow-sm text-gray-900 border border-gray-200/50",
    inactive: "text-gray-600 hover:text-gray-900"
  },

  // Form patterns optimized for mobile
  form: {
    container: "space-y-6 p-4 md:p-6 lg:p-8",
    field: "space-y-2",
    input: [
      "w-full h-12 px-4 rounded-xl border border-gray-200",
      "focus:border-gray-400 focus:ring-2 focus:ring-gray-100",
      "transition-all duration-200 touch-target",
      "text-base" // Prevent zoom on iOS
    ].join(" "),
    button: [
      "w-full h-12 rounded-xl font-semibold",
      "transition-all duration-200 touch-target",
      "active:scale-[0.98]"
    ].join(" ")
  },

  // List patterns that work beautifully on mobile
  list: {
    container: "space-y-3 p-4 md:p-6",
    item: [
      "flex items-center justify-between p-4 rounded-xl",
      "bg-white border border-gray-200/50 shadow-sm",
      "hover:shadow-md hover:border-gray-300/50",
      "transition-all duration-200 touch-target"
    ].join(" "),
    content: "flex-1 min-w-0 pr-4",
    action: "flex-shrink-0"
  },

  // Modal patterns with mobile-first approach
  modal: {
    overlay: "fixed inset-0 z-50 bg-black/20 backdrop-blur-sm",
    content: [
      // Mobile: bottom sheet
      "fixed bottom-0 left-0 right-0 z-50",
      "bg-white rounded-t-2xl shadow-xl",
      "max-h-[90vh] overflow-y-auto",
      "safe-bottom",
      // Tablet+: centered modal
      "md:relative md:bottom-auto md:left-auto md:right-auto",
      "md:max-w-lg md:mx-auto md:mt-20 md:rounded-2xl",
      "md:max-h-[85vh]"
    ].join(" "),
    header: "flex items-center justify-between p-6 border-b border-gray-100",
    body: "p-6 space-y-4",
    footer: "p-6 border-t border-gray-100 space-y-3"
  }
} as const

// Export individual pattern functions for easier usage
export const getVisualRetreatCard = (variant: 'base' | 'interactive' | 'floating' = 'base') => {
  const base = visualRetreat.card.base
  switch (variant) {
    case 'interactive':
      return `${base} ${visualRetreat.card.interactive}`
    case 'floating':
      return `${base} ${visualRetreat.card.floating}`
    default:
      return base
  }
}

export const getVisualRetreatTab = (isActive: boolean) => {
  return `${visualRetreat.tabs.item} ${isActive ? visualRetreat.tabs.active : visualRetreat.tabs.inactive}`
}

export const getVisualRetreatModal = (section: 'overlay' | 'content' | 'header' | 'body' | 'footer') => {
  return visualRetreat.modal[section]
}

export const getVisualRetreatForm = (element: 'container' | 'field' | 'input' | 'button') => {
  return visualRetreat.form[element]
}

export const getVisualRetreatList = (element: 'container' | 'item' | 'content' | 'action') => {
  return visualRetreat.list[element]
}
