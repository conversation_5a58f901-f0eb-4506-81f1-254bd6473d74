/**
 * Test component for Analyst Research Tab
 * 
 * This component demonstrates the new Analyst Research functionality
 * with mock data to verify the UI/UX implementation.
 */

"use client"

import { useState } from "react"
import { AnalystResearchTab } from "@/components/core/deals/deal-detail/analyst-research-tab"
import { DealDetailData } from "@/lib/types/deal-detail"

// Mock research data for testing
const mockDealData: DealDetailData = {
  id: "test-deal-123",
  org_id: "test-org-456",
  form_id: "test-form-789",
  submission_ids: ["test-submission-001"],
  company_name: "TractionX",
  stage: "Series A",
  sector: ["AI/ML", "SaaS", "B2B"],
  company_website: "https://tractionx.ai",
  status: "new" as any,
  created_at: Date.now(),
  updated_at: Date.now(),
  timeline: [],
  founders: [],
  external_signals: [],
  documents: [],
  chat_history: [],
  analyst_research: {
    deal_id: "test-deal-123",
    competitors: {
      competitors: [
        {
          name: "DealflowAI",
          website: "https://dealflowai.com",
          description: "AI-powered deal pipeline management for venture capital firms",
          comparison: "DealflowAI targets larger VCs with enterprise features, while TractionX focuses on emerging managers with intuitive workflows",
          sources: ["https://dealflowai.com", "https://crunchbase.com/organization/dealflowai"]
        },
        {
          name: "Foundersuite",
          website: "https://foundersuite.com",
          description: "Investor CRM and deal management platform",
          comparison: "Foundersuite offers broader CRM capabilities, while TractionX specializes in AI-driven deal analysis",
          sources: ["https://foundersuite.com", "https://techcrunch.com/foundersuite-funding"]
        },
        {
          name: "Visible",
          website: "https://visible.vc",
          description: "Investor reporting and portfolio management platform",
          comparison: "Visible focuses on portfolio reporting, while TractionX emphasizes deal sourcing and evaluation",
          sources: ["https://visible.vc", "https://www.forbes.com/visible-vc-growth"]
        }
      ]
    },
    market: {
      market_trends: [
        {
          summary: "The global venture capital software market is experiencing 25% YoY growth, driven by increased VC activity and demand for data-driven investment decisions",
          sources: ["https://www.mckinsey.com/vc-software-trends", "https://pitchbook.com/news/reports/q3-2024-vc-market"]
        },
        {
          summary: "AI-powered deal sourcing tools are becoming essential, with 78% of VCs planning to increase technology spending in 2024",
          sources: ["https://www.bcg.com/ai-venture-capital", "https://www.preqin.com/insights/research/reports/vc-tech-adoption"]
        },
        {
          summary: "Emerging managers (sub-$100M funds) represent the fastest-growing segment, creating demand for affordable, sophisticated tools",
          sources: ["https://www.cambridge.org/emerging-managers-study", "https://www.sec.gov/emerging-fund-managers"]
        }
      ]
    },
    news: {
      news_signals: [
        {
          headline: "Sequoia Capital Leads $50M Series B in AI-Powered VC Platform",
          summary: "Major VC firms are investing heavily in technology platforms that enhance deal sourcing and evaluation capabilities",
          url: "https://techcrunch.com/sequoia-ai-vc-platform",
          date: "2024-01-15"
        },
        {
          headline: "Emerging Managers Drive Record VC Software Adoption",
          summary: "Smaller funds are increasingly adopting sophisticated software tools previously used only by large institutional investors",
          url: "https://www.bloomberg.com/emerging-managers-tech",
          date: "2024-01-10"
        },
        {
          headline: "AI Deal Scoring Reduces Investment Decision Time by 60%",
          summary: "New research shows AI-powered analysis significantly accelerates the investment evaluation process",
          url: "https://www.wsj.com/ai-deal-scoring-efficiency",
          date: "2024-01-08"
        }
      ]
    },
    summary: {
      executive_summary: "TractionX is uniquely positioned at the intersection of AI/ML innovation and venture capital workflow optimization. The company addresses a critical pain point for emerging managers who lack access to enterprise-grade deal management tools. With the VC software market growing at 25% annually and 78% of firms increasing technology spending, TractionX's AI-powered approach to deal sourcing and evaluation represents a significant market opportunity. The platform's focus on intuitive workflows and affordable pricing differentiates it from enterprise-focused competitors like DealflowAI, while its AI capabilities provide superior analysis compared to traditional CRM solutions. Recent market validation includes increased adoption among emerging managers and growing recognition that AI-powered tools can reduce investment decision time by up to 60%. TractionX's Series A timing aligns perfectly with market demand for sophisticated, accessible VC technology solutions.",
      sources: [
        "https://www.mckinsey.com/vc-software-trends",
        "https://pitchbook.com/news/reports/q3-2024-vc-market",
        "https://www.bcg.com/ai-venture-capital",
        "https://techcrunch.com/sequoia-ai-vc-platform"
      ]
    },
    generated_at: Date.now(),
    version: "v1.0"
  }
}

export default function TestAnalystResearch() {
  const [showTest, setShowTest] = useState(false)

  if (!showTest) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center space-y-4">
          <h1 className="text-2xl font-bold text-gray-900">Analyst Research Test</h1>
          <p className="text-gray-600">Click below to test the new Analyst Research tab</p>
          <button
            onClick={() => setShowTest(true)}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Launch Test
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8">
        <div className="mb-6">
          <button
            onClick={() => setShowTest(false)}
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            ← Back to Test Menu
          </button>
        </div>
        
        <AnalystResearchTab deal={mockDealData} />
      </div>
    </div>
  )
}
