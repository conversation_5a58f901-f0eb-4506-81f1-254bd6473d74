"use client"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { motion } from "framer-motion"
import * as z from "zod"
import Link from "next/link"

import { cn } from "@/lib/utils"
import { visualRetreat } from "@/lib/utils/responsive"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { Icons } from "@/components/icons"
import { useAuth } from "@/lib/auth-context"
import { AuthCard } from "./auth-card"

const registerSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string().min(8, "Please confirm your password"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type RegisterFormValues = z.infer<typeof registerSchema>

export function RegisterForm() {
  const { register } = useAuth()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  })

  async function onSubmit(data: RegisterFormValues) {
    setIsLoading(true)

    try {
      const result = await register(data.name, data.email, data.password)
      
      if (result.success) {
        toast({
          title: "Account created!",
          description: "Welcome to TractionX. You've been successfully registered.",
        })
        
        // Redirect to dashboard or onboarding
        router.push(result.redirectUrl || "/dashboard")
      }
    } catch (error) {
      console.error("Registration error:", error)
      toast({
        title: "Registration failed",
        description: error instanceof Error ? error.message : "Please check your information and try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AuthCard>
      <form onSubmit={form.handleSubmit(onSubmit)} className={visualRetreat.form.container}>
        <div className="space-y-6">
          {/* Name Field */}
          <div className={visualRetreat.form.field}>
            <Label htmlFor="name" className="text-sm font-medium text-gray-700">
              Full Name
            </Label>
            <Input
              id="name"
              type="text"
              placeholder="Enter your full name"
              autoCapitalize="words"
              autoComplete="name"
              disabled={isLoading}
              className={cn(
                visualRetreat.form.input,
                "bg-white/50 border-white/20 focus:border-blue-500/50 focus:ring-blue-500/20"
              )}
              {...form.register("name")}
            />
            {form.formState.errors.name && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-500"
              >
                {form.formState.errors.name.message}
              </motion.p>
            )}
          </div>

          {/* Email Field */}
          <div className={visualRetreat.form.field}>
            <Label htmlFor="email" className="text-sm font-medium text-gray-700">
              Email
            </Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              autoCapitalize="none"
              autoComplete="email"
              autoCorrect="off"
              disabled={isLoading}
              className={cn(
                visualRetreat.form.input,
                "bg-white/50 border-white/20 focus:border-blue-500/50 focus:ring-blue-500/20"
              )}
              {...form.register("email")}
            />
            {form.formState.errors.email && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-500"
              >
                {form.formState.errors.email.message}
              </motion.p>
            )}
          </div>

          {/* Password Field */}
          <div className={visualRetreat.form.field}>
            <Label htmlFor="password" className="text-sm font-medium text-gray-700">
              Password
            </Label>
            <Input
              id="password"
              type="password"
              placeholder="Create a strong password"
              autoComplete="new-password"
              disabled={isLoading}
              className={cn(
                visualRetreat.form.input,
                "bg-white/50 border-white/20 focus:border-blue-500/50 focus:ring-blue-500/20"
              )}
              {...form.register("password")}
            />
            {form.formState.errors.password && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-500"
              >
                {form.formState.errors.password.message}
              </motion.p>
            )}
          </div>

          {/* Confirm Password Field */}
          <div className={visualRetreat.form.field}>
            <Label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">
              Confirm Password
            </Label>
            <Input
              id="confirmPassword"
              type="password"
              placeholder="Confirm your password"
              autoComplete="new-password"
              disabled={isLoading}
              className={cn(
                visualRetreat.form.input,
                "bg-white/50 border-white/20 focus:border-blue-500/50 focus:ring-blue-500/20"
              )}
              {...form.register("confirmPassword")}
            />
            {form.formState.errors.confirmPassword && (
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-sm text-red-500"
              >
                {form.formState.errors.confirmPassword.message}
              </motion.p>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          disabled={isLoading}
          className={cn(
            visualRetreat.form.button,
            "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium shadow-lg shadow-blue-500/25"
          )}
        >
          {isLoading ? (
            <>
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              Creating account...
            </>
          ) : (
            "Create account"
          )}
        </Button>

        {/* Sign In Link */}
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            Already have an account?{" "}
            <Link
              href="/login"
              className="text-blue-600 hover:text-blue-500 font-medium transition-colors"
            >
              Sign in
            </Link>
          </p>
        </div>
      </form>
    </AuthCard>
  )
}
