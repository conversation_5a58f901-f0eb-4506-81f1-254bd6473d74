"use client"

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Upload, FileText, Mail, Globe, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface NewDealModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: NewDealData) => void;
  availableForms?: Array<{
    id: string;
    name: string;
    description?: string;
    is_active: boolean;
  }>;
}

interface NewDealData {
  company_name: string;
  company_website?: string;
  contact_email: string; // Required for invite functionality
  form_id: string; // Required - which form to send
  stage?: string;
  sector?: string;
  pitch_deck?: File;
  notes?: string;
  send_invite?: boolean; // Whether to send invite email
}

const STAGES = [
  'PRE SEED',
  'SEED',
  'SERIES A',
  'SERIES B',
  'SERIES C',
  'GROWTH',
  'LATE STAGE'
];

const SECTORS = [
  'FINTECH',
  'HEALTHTECH',
  'EDTECH',
  'PROPTECH',
  'FOODTECH',
  'MOBILITY',
  'ENTERPRISE',
  'CONSUMER',
  'DEEPTECH',
  'CLIMATE',
  'OTHER'
];

export function NewDealModal({ open, onOpenChange, onSubmit, availableForms = [] }: NewDealModalProps) {
  const [formData, setFormData] = useState<NewDealData>({
    company_name: '',
    company_website: '',
    contact_email: '',
    form_id: '',
    stage: '',
    sector: '',
    notes: '',
    send_invite: true
  });
  const [pitchDeck, setPitchDeck] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      await onSubmit({
        ...formData,
        pitch_deck: pitchDeck || undefined
      });
      
      // Reset form
      setFormData({
        company_name: '',
        company_website: '',
        contact_email: '',
        form_id: '',
        stage: '',
        sector: '',
        notes: '',
        send_invite: true
      });
      setPitchDeck(null);
      onOpenChange(false);
    } catch (error) {
      console.error('Error creating deal:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setPitchDeck(file);
    }
  };

  const removePitchDeck = () => {
    setPitchDeck(null);
    // Reset the file input
    const fileInput = document.getElementById('pitch-deck-upload') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[650px] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-3">
          <DialogTitle className="text-xl font-semibold text-foreground">
            Add New Deal
          </DialogTitle>
          <DialogDescription className="text-muted-foreground leading-relaxed">
            Create a new investment opportunity and send a warm invite to the startup with your application form.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Company Name */}
          <div className="space-y-2.5">
            <Label htmlFor="company_name" className="text-sm font-medium text-foreground">
              Company Name *
            </Label>
            <Input
              id="company_name"
              value={formData.company_name}
              onChange={(e) => setFormData(prev => ({ ...prev, company_name: e.target.value }))}
              placeholder="Enter company name"
              required
              className="w-full h-10"
            />
          </div>

          {/* Website and Email Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Website */}
            <div className="space-y-2.5">
              <Label htmlFor="company_website" className="text-sm font-medium text-foreground">
                Website
              </Label>
              <div className="relative">
                <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="company_website"
                  value={formData.company_website}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      company_website: e.target.value,
                    }))
                  }
                  placeholder="company.com"
                  className="pl-10 h-10"
                  autoComplete="off"
                />
              </div>
            </div>

            {/* Contact Email */}
            <div className="space-y-2.5">
              <Label htmlFor="contact_email" className="text-sm font-medium text-foreground">
                Contact Email *
              </Label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="contact_email"
                  type="email"
                  value={formData.contact_email}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      contact_email: e.target.value,
                    }))
                  }
                  placeholder="<EMAIL>"
                  className="pl-10 h-10"
                  autoComplete="off"
                  required
                />
              </div>
              <p className="text-xs text-muted-foreground">
                We'll send the application form to this email
              </p>
            </div>
          </div>

          {/* Form Selection */}
          <div className="space-y-2.5">
            <Label className="text-sm font-medium text-foreground">
              Application Form *
            </Label>
            <Select
              value={formData.form_id}
              onValueChange={(value) => setFormData(prev => ({ ...prev, form_id: value }))}
            >
              <SelectTrigger className="h-10">
                <SelectValue placeholder="Select form to send" />
              </SelectTrigger>
              <SelectContent>
                {availableForms.filter(form => form.is_active).map((form) => (
                  <SelectItem key={form.id} value={form.id}>
                    <div className="flex flex-col gap-1">
                      <span className="font-medium text-foreground">{form.name}</span>
                      {form.description && (
                        <span className="text-xs text-muted-foreground">{form.description}</span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              This form will be sent to the startup for completion
            </p>
          </div>

          {/* Stage and Sector Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2.5">
              <Label className="text-sm font-medium text-foreground">Stage</Label>
              <Select value={formData.stage} onValueChange={(value) => setFormData(prev => ({ ...prev, stage: value }))}>
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Select stage" />
                </SelectTrigger>
                <SelectContent>
                  {STAGES.map((stage) => (
                    <SelectItem key={stage} value={stage}>
                      {stage}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2.5">
              <Label className="text-sm font-medium text-foreground">Sector</Label>
              <Select value={formData.sector} onValueChange={(value) => setFormData(prev => ({ ...prev, sector: value }))}>
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Select sector" />
                </SelectTrigger>
                <SelectContent>
                  {SECTORS.map((sector) => (
                    <SelectItem key={sector} value={sector}>
                      {sector}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Pitch Deck Upload */}
          <div className="space-y-2.5">
            <Label className="text-sm font-medium text-foreground">
              Pitch Deck (Optional)
            </Label>
            {pitchDeck ? (
              <div className="border border-border rounded-lg p-4 bg-muted/30">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <FileText className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium text-foreground">{pitchDeck.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {(pitchDeck.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={removePitchDeck}
                    className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ) : (
              <div className="border-2 border-dashed border-border rounded-lg p-6 text-center hover:border-primary/50 transition-colors cursor-pointer group">
                <input
                  type="file"
                  accept=".pdf,.ppt,.pptx"
                  onChange={handleFileChange}
                  className="hidden"
                  id="pitch-deck-upload"
                />
                <label htmlFor="pitch-deck-upload" className="cursor-pointer">
                  <div className="flex flex-col items-center gap-2">
                    <div className="p-3 rounded-full bg-muted group-hover:bg-primary/10 transition-colors">
                      <Upload className="h-6 w-6 text-muted-foreground group-hover:text-primary transition-colors" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-foreground mb-1">
                        Upload pitch deck
                      </p>
                      <p className="text-xs text-muted-foreground">
                        PDF, PPT, or PPTX files only
                      </p>
                    </div>
                  </div>
                </label>
              </div>
            )}
          </div>

          {/* Notes */}
          <div className="space-y-2.5">
            <Label htmlFor="notes" className="text-sm font-medium text-foreground">
              Notes (Optional)
            </Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              placeholder="Add any additional notes about this opportunity..."
              rows={3}
              className="resize-none min-h-[80px]"
            />
          </div>

          <DialogFooter className="gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
              className="min-w-[100px]"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!formData.company_name || !formData.contact_email || !formData.form_id || isSubmitting}
              className="min-w-[140px]"
            >
              {isSubmitting ? 'Creating...' : 'Create & Send Invite'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
