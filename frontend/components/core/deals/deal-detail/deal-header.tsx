"use client"

import { useState } from "react"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  ChevronLeft,
  Share2,
  MessageSquare,
  MoreHorizontal,
  Flag,
  StickyNote,
  Star,
  ExternalLink,
  Copy,
  Download
} from "lucide-react"
import { cn } from "@/lib/utils"
import { visualRetreat } from "@/lib/utils/responsive"
import { DealDetailData } from "@/lib/types/deal-detail"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { toast } from "@/components/ui/use-toast"

interface DealHeaderProps {
  deal: DealDetailData
}

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'new':
      return 'bg-blue-50/80 text-blue-700 border-blue-200/60 before:bg-blue-500'
    case 'active':
      return 'bg-emerald-50/80 text-emerald-700 border-emerald-200/60 before:bg-emerald-500'
    case 'triage':
      return 'bg-amber-50/80 text-amber-700 border-amber-200/60 before:bg-amber-500'
    case 'completed':
      return 'bg-gray-50/80 text-gray-700 border-gray-200/60 before:bg-gray-500'
    case 'flagged':
      return 'bg-red-50/80 text-red-700 border-red-200/60 before:bg-red-500'
    case 'hard_pass':
      return 'bg-red-50/80 text-red-700 border-red-200/60 before:bg-red-500'
    default:
      return 'bg-gray-50/80 text-gray-700 border-gray-200/60 before:bg-gray-500'
  }
}

const getStageColor = (stage: string) => {
  switch (stage?.toLowerCase()) {
    case 'pre-seed':
      return 'bg-purple-50/80 text-purple-700 border-purple-200/60'
    case 'seed':
      return 'bg-blue-50/80 text-blue-700 border-blue-200/60'
    case 'series a':
      return 'bg-emerald-50/80 text-emerald-700 border-emerald-200/60'
    case 'series b':
      return 'bg-orange-50/80 text-orange-700 border-orange-200/60'
    case 'series c':
      return 'bg-red-50/80 text-red-700 border-red-200/60'
    default:
      return 'bg-gray-50/80 text-gray-700 border-gray-200/60'
  }
}

const getScoreColor = (score: number) => {
  if (score >= 80) return 'from-green-500 to-emerald-600'
  if (score >= 60) return 'from-yellow-500 to-orange-500'
  return 'from-red-500 to-red-600'
}

const formatTimeAgo = (timestamp: string | number) => {
  const date = new Date(typeof timestamp === 'string' ? timestamp : timestamp * 1000)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return 'just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  return `${Math.floor(diffInSeconds / 86400)}d ago`
}

export function DealHeader({ deal }: DealHeaderProps) {
  const [isStarred, setIsStarred] = useState(false)

  const handleStarToggle = () => {
    setIsStarred(!isStarred)
    toast({
      title: isStarred ? "Removed from favorites" : "Added to favorites",
      description: `${deal.company_name} has been ${isStarred ? 'removed from' : 'added to'} your favorites.`,
    })
  }

  const handleShare = () => {
    navigator.clipboard.writeText(window.location.href)
    toast({
      title: "Link copied",
      description: "Deal link has been copied to your clipboard.",
    })
  }

  const handleCreateMemo = () => {
    toast({
      title: "AI Memo Generation",
      description: "Starting AI-powered memo generation...",
    })
    // TODO: Implement deal memo creation
  }

  const humanize = (key: string) => {
    return key
      .split('_')
      .map(part =>
        part.length <= 2
          ? part.toUpperCase()                                   // e.g. 'ai' → 'AI'
          : part[0].toUpperCase() + part.slice(1).toLowerCase()  // e.g. 'pre' → 'Pre'
      )
      .join('-');                                              // e.g. ['Pre','Seed'] → 'Pre-Seed'
  }

  const overallScore = deal.score_breakdown?.overall_score || 0
  const foundersCount = deal.founders?.length || 0
  const documentsCount = deal.documents?.length || 0
  const signalsCount = deal.external_signals?.length || 0
  const lastUpdated = formatTimeAgo(deal.updated_at)

  return (
    <div className="space-y-6">
      {/* Minimalist Breadcrumb */}
      <div className="flex items-center">
        <Link
          href="/deals"
          className="group flex items-center text-sm text-gray-500 hover:text-gray-700 transition-colors"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          <span>Back to Deals</span>
        </Link>
      </div>

      {/* Minimalist Hero Section */}
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
        {/* Left Side - Company Information */}
        <div className="space-y-4 flex-1">
          {/* Company Name - Clean Typography */}
          <div className="space-y-2">
            <h1 className="text-3xl font-bold text-gray-900">
              {deal.company_name || 'Unnamed Company'}
            </h1>

            {/* Website link if available */}
            {deal.company_website && (
              <a
                href={deal.company_website}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-700 transition-colors text-sm"
              >
                <span>{deal.company_website.replace(/^https?:\/\//, '')}</span>
                <ExternalLink className="h-3 w-3" />
              </a>
            )}
          </div>

          {/* Clean Status Chips */}
          <div className="flex flex-wrap items-center gap-3">
            {/* Status Badge */}
            <Badge
              className={cn(
                "relative pl-6 font-medium",
                getStatusColor(deal.status),
                "before:absolute before:left-2.5 before:top-1/2 before:-translate-y-1/2 before:w-2 before:h-2 before:rounded-full"
              )}
            >
              {deal.status.charAt(0).toUpperCase() + deal.status.slice(1).replace('_', ' ')}
            </Badge>

            {/* Stage Badge */}
            {deal.stage && (
              <Badge className={cn("font-medium", getStageColor(deal.stage))}>
                {humanize(deal.stage)}
              </Badge>
            )}

            {/* Sector Badges */}
            {deal.sector && (
              <>
                {Array.isArray(deal.sector) ? (
                  <>
                    <Badge variant="secondary" className="bg-gray-100 text-gray-700">
                      {humanize(deal.sector[0])}
                    </Badge>
                    {deal.sector.length > 1 && (
                      <Badge
                        variant="secondary"
                        className="bg-gray-100 text-gray-700 cursor-pointer hover:bg-gray-200 transition-colors"
                        title={deal.sector.slice(1).map(s => humanize(s)).join(', ')}
                      >
                        {deal.sector.length === 2 ? humanize(deal.sector[1]) : `+${deal.sector.length - 1} more`}
                      </Badge>
                    )}
                  </>
                ) : (
                  <Badge variant="secondary" className="bg-gray-100 text-gray-700">
                    {deal.sector}
                  </Badge>
                )}
              </>
            )}

            {/* Score Badge */}
            {overallScore > 0 && (
              <Badge
                className={cn(
                  "px-3 py-1 text-lg font-bold border-2 bg-white",
                  overallScore >= 80 ? "border-emerald-500 text-emerald-700" :
                  overallScore >= 60 ? "border-amber-500 text-amber-700" :
                  "border-red-500 text-red-700"
                )}
              >
                {overallScore}
              </Badge>
            )}
          </div>

          {/* Meta Information */}
          <div className="text-sm text-gray-500">
            {foundersCount} founder{foundersCount !== 1 ? 's' : ''} · {documentsCount} document{documentsCount !== 1 ? 's' : ''} · {signalsCount} signal{signalsCount !== 1 ? 's' : ''} · Updated {lastUpdated}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-3 lg:flex-shrink-0">
          {/* Star Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleStarToggle}
            className={cn(
              "transition-colors",
              isStarred ? "bg-amber-50 border-amber-200 text-amber-700" : ""
            )}
          >
            <Star className={cn("h-4 w-4", isStarred && "fill-current")} />
          </Button>

          {/* AI Memo Button */}
          <Button
            onClick={handleCreateMemo}
            className="gap-2 bg-blue-600 hover:bg-blue-700 text-white"
          >
            <StickyNote className="h-4 w-4" />
            <span className="hidden sm:inline">AI Memo</span>
            <span className="sm:hidden">Memo</span>
          </Button>

          {/* Share Button */}
          <Button
            variant="ghost"
            onClick={handleShare}
            className="gap-2"
          >
            <Share2 className="h-4 w-4" />
            <span className="hidden sm:inline">Share</span>
          </Button>

          {/* More Actions */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem>
                <Copy className="h-4 w-4 mr-2" />
                Copy Link
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Flag className="h-4 w-4 mr-2" />
                Move to Folder
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600">
                <Flag className="h-4 w-4 mr-2" />
                Flag Deal
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  )
}
