"use client"

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Plus, Target, Edit, MoreHorizontal, Trash2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { EmptyPlaceholder } from '@/components/empty-placeholder';
import { DashboardHeader } from '@/components/header';
import { toast } from '@/components/ui/use-toast';
import { ThesisAPI } from '@/lib/api/thesis-api';
import { InvestmentThesis } from '@/lib/types/thesis';
import { useAuth } from '@/lib/auth-context';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';

export function ThesesList() {
  const [theses, setTheses] = useState<InvestmentThesis[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { isAuthenticated } = useAuth();
  const router = useRouter();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [thesisToDelete, setThesisToDelete] = useState<InvestmentThesis | null>(null);
  const [deleting, setDeleting] = useState(false);

  console.log('📋 ThesesList component loaded');

  const handleCreateThesis = () => {
    console.log('🚀 Attempting to navigate to /theses/new');
    try {
      router.push('/theses/new');
      console.log('✅ Navigation initiated');
    } catch (error) {
      console.error('❌ Navigation failed:', error);
    }
  };

  // Fetch theses from the API
  useEffect(() => {
    const fetchTheses = async () => {
      try {
        console.log("Fetching theses from API");
        setIsLoading(true);
        const thesesList = await ThesisAPI.listTheses();
        console.log("Theses fetched:", thesesList);

        // Ensure we always have an array
        if (Array.isArray(thesesList)) {
          setTheses(thesesList);
        } else {
          console.warn("API returned non-array response:", thesesList);
          setTheses([]);
        }
      } catch (error) {
        console.error("Error fetching theses:", error);
        setTheses([]); // Reset to empty array on error
        toast({
          title: "Error",
          description: "Failed to load theses. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (isAuthenticated) {
      fetchTheses();
    }
  }, [isAuthenticated]);

  const getStatusBadge = (status: boolean) => {
    switch (status) {
      case true:
        return <Badge variant="default">Active</Badge>;
      case false:
        return <Badge variant="secondary">Draft</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div>
        <DashboardHeader
          heading="Investment Theses"
          text="Create and manage your investment theses."
        >
          <Button onClick={handleCreateThesis}>
            <Plus className="h-4 w-4" />
          </Button>
        </DashboardHeader>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-muted rounded"></div>
                  <div className="h-3 bg-muted rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (theses.length === 0) {
    return (
      <div>
        <DashboardHeader
          heading="Investment Theses"
          text="Create and manage your investment theses."
        >
          <Button onClick={handleCreateThesis}>
            <Plus className="h-4 w-4" />
          </Button>
        </DashboardHeader>

        <EmptyPlaceholder>
          <EmptyPlaceholder.Icon name="page" />
          <EmptyPlaceholder.Title>No theses created</EmptyPlaceholder.Title>
          <EmptyPlaceholder.Description>
            You don&apos;t have any investment theses yet. Create a thesis to define your investment strategy.
          </EmptyPlaceholder.Description>
          <Button variant="outline" onClick={handleCreateThesis}>
            <Plus className="h-4 w-4" />
          </Button>
        </EmptyPlaceholder>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <DashboardHeader
        heading="Investment Theses"
        text="Create and manage your investment theses."
      >
        <Button onClick={handleCreateThesis}>
          <Plus className="h-4 w-4" />
        </Button>
      </DashboardHeader>

      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 auto-rows-fr">
        {theses.map((thesis) => (
          <Card key={thesis._id || thesis.id} className="hover:shadow-lg transition-shadow flex flex-col h-full">
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <CardTitle className="text-xl">{thesis.name}</CardTitle>
                  <div className="flex items-center gap-3 justify-end">
                    {getStatusBadge(thesis.is_active)}
                    <div className="w-full flex justify-end">
                    <span className="text-sm text-muted-foreground text-right block">
                      Updated {thesis.updated_at ? new Date(thesis.updated_at * 1000).toLocaleDateString() : 'N/A'}
                    </span>
                    </div>
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem asChild>
                      <Link href={`/theses/${thesis._id || thesis.id}`}>
                        <Edit className="h-4 w-4 mr-2 text-muted-foreground" />
                        Edit
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => {
                        setThesisToDelete(thesis);
                        setDeleteDialogOpen(true);
                      }}
                      className="text-destructive flex w-full"
                    >
                      <Trash2 className="h-4 w-4 mr-2 text-destructive" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>

            <CardContent className="space-y-4 flex-grow flex flex-col">
              <CardDescription className="line-clamp-2">
                {thesis.description}
              </CardDescription>

              <div className="space-y-2">
                {/*<div className="flex items-center justify-between text-sm">*/}
                {/*  <span className="text-muted-foreground">Form Name:</span>*/}
                {/*  <span className="font-medium text-xs">{thesis.form_id}</span>*/}
                {/*</div>*/}

                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Rules:</span>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      <Target className="h-3 w-3 mr-1" />
                      {thesis.match_rules?.length || 0} match
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {thesis.scoring_rules?.length || 0} scoring
                    </Badge>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Status:</span>
                  <span className={`font-medium ${thesis.is_active ? 'text-green-600' : 'text-gray-500'}`}>
                    {thesis.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>

              <div className="pt-2 mt-auto">
                <Link href={`/theses/${thesis._id || thesis.id}`}>
                  <Button variant="outline" size="sm" className="w-full">
                    <Edit className="h-4 w-4" />
                        Edit Thesis
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <ConfirmDialog
        open={deleteDialogOpen}
        title="Delete Thesis"
        description={`Are you sure you want to delete the thesis "${thesisToDelete?.name || ''}"? This action cannot be undone.`}
        onCancel={() => { setDeleteDialogOpen(false); setThesisToDelete(null); }}
        onConfirm={async () => {
          if (!thesisToDelete) return;
          setDeleting(true);
          try {
            const id = String(thesisToDelete._id || thesisToDelete.id);
            await ThesisAPI.deleteThesis(id);
            setTheses(theses => theses.filter(t => String(t._id || t.id) !== id));
            toast({
              title: 'Thesis deleted',
              description: 'The thesis has been deleted successfully.'
            });
          } catch (error) {
            toast({
              title: 'Error',
              description: 'Failed to delete thesis. Please try again.',
              variant: 'destructive'
            });
          } finally {
            setDeleting(false);
            setDeleteDialogOpen(false);
            setThesisToDelete(null);
          }
        }}
        loading={deleting}
      />
    </div>
  );
}
